import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useAuth } from "../../contexts/AuthContext";
import { cn } from "../../lib/utils";
import { usePageTitle } from "../../hooks/usePageTitle";
import { SEOHead } from "../seo/SEOHead";
import { getPageSEOData } from "../../lib/seo";
import { LoadingButton } from "../ui/LoadingSpinner";
import { BrimBagLogo } from "../ui/BrimBagLogo";

const _forgotPasswordSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

type ForgotPasswordFormData = z.infer<typeof _forgotPasswordSchema>;

export function ForgotPasswordForm() {
  const { resetPassword, actionLoading } = useAuth();
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Set page title
  usePageTitle("Reset Password");

  // Generate SEO metadata
  const seoMetadata = getPageSEOData("/forgot-password");

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    getValues,
  } = useForm<ForgotPasswordFormData>();

  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      await resetPassword(data.email);
      setIsSubmitted(true);
    } catch (error: any) {
      const errorMessage =
        error?.message || "An error occurred while sending reset email";
      setError("email", { message: errorMessage });
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-dark-900 dark:to-dark-800 py-12 px-4 sm:px-6 lg:px-8 transition-theme duration-theme">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center transition-theme duration-theme">
              <svg
                className="h-8 w-8 text-green-600 dark:text-green-400 transition-theme duration-theme"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h2 className="mt-6 text-3xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
              Check your email
            </h2>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
              We've sent a password reset link to{" "}
              <span className="font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                {getValues("email")}
              </span>
            </p>
          </div>

          <div className="card p-8">
            <div className="text-center space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 transition-theme duration-theme">
                <p className="text-sm text-blue-800 dark:text-blue-300 transition-theme duration-theme">
                  <strong>Didn't receive the email?</strong>
                  <br />
                  Check your spam folder or try again with a different email
                  address.
                </p>
              </div>

              <button
                onClick={() => setIsSubmitted(false)}
                className="text-sm text-primary-600 hover:text-primary-500 font-medium"
              >
                Try again with a different email
              </button>

              <div className="pt-4 border-t border-gray-200">
                <Link
                  to="/login"
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
                >
                  Back to sign in
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Meta Tags */}
      <SEOHead metadata={seoMetadata} />

      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-dark-900 dark:to-dark-800 py-12 px-4 sm:px-6 lg:px-8 transition-theme duration-theme">
        <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 rounded-full flex items-center justify-center transition-theme duration-theme">
            <BrimBagLogo size="xl" variant="white" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
            Forgot your password?
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
            No worries! Enter your email address and we'll send you a link to
            reset your password.
          </p>
        </div>

        <div className="card p-8">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme"
              >
                Email address
              </label>
              <input
                {...register("email")}
                type="email"
                autoComplete="email"
                disabled={actionLoading}
                className={cn(
                  "input-field",
                  errors.email && "border-red-300 focus:ring-red-500",
                  actionLoading && "opacity-60 cursor-not-allowed"
                )}
                placeholder="Enter your email address"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
                  {errors.email.message}
                </p>
              )}
            </div>

            <LoadingButton
              type="submit"
              loading={actionLoading}
              loadingText="Sending reset link..."
              variant="primary"
              className="w-full"
            >
              Send reset link
            </LoadingButton>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500 dark:text-gray-400">
                  Remember your password?
                </span>
              </div>
            </div>

            <div className="mt-6">
              <Link
                to="/login"
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
              >
                Back to sign in
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
