{"name": "brimbag", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@supabase/supabase-js": "^2.50.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "pdfjs-dist": "^5.3.31", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-file-viewer": "^1.2.1", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-pdf": "^10.0.1", "react-router-dom": "^7.6.3", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "vite-plugin-pwa": "^1.0.1"}}